1.0.70
1. 新增BAW 4C電表。
2. 修正M4M電表讀值。
3. 修尹PM210電表讀值。
4. 新增CC130數位水表。

1.0.69
1. 修正Soyal安心簡訊功能(KEVIN)。
2. 新增 TCS5282 CO2。
3. 新增 M4m,kt-mk3 兩種電表。
4. 修正 PM210讀值問題。
5. 修正 TH API的boolean問題。
6. 減少 CURL Log數量。

1.0.68
1. 新增 CicBaw2c 電表支援。
2. 新增 永嘉 3in1 co支援。
3. 關閉對講機IP監控功能，現只會顯示註冊/通話中兩種狀態。
4. 新增 TCS30A22支援。
5. 新增一支safety_message配合外掛用的漢軍門禁+馥鴻及漢軍門禁+昇銳。
6. 修正通用AI不會警報的問題。
7. 修正首頁的KWH顯示小數點過長問題。
8. 修正天氣看板(中路一)的顯示問題。
9. 新增VRM MP8支援。
10. 優化通用AI的LOG數量。
11. 適配多媒體系統的API更新。
12. 新增 DAE PM 210支援。
13. 電表紀錄新增圓餅圖。
14. 加大電表數值單位，避免溢位。

1.0.67
1. 修正通用水表不能設定在首頁出現的問題及讀值異常。
2. 修正永嘉CO數值異常。
3. 新增ORTIS電梯支援。
4. 修正Acuvim電表讀值。



1.0.66
1. 新增四種永嘉空氣品質裝置。
2. 修正首頁電表清單的過濾條件。
3. 新增永嘉照度計。
4. 新增AcuVim電表。
5. 修改Weema智慧社區推播邏輯。

1.0.65 ㊣㊣★★★此版為特殊退版，需稍加觀察測試★★★★㊣㊣
1. 從1.0.64版回朔回1.0.59版。
2. 修正警報時攝影機跳不出來的問題。
3. 新增試用時效及驗證機制功能。
4. 適配BA小APP(新Weema社區)。

1.0.59
1. 修正無聲BUG。
2. 修正LOG跟時間設定問題。
3. 修正錯誤的跳窗URL問題。
4. 優化LOG相關速度。
5. 修正通用太陽能讀值。
6. 加長main_elec欄位。
7. 修正需量問題。
8. 放慢RS485速度。
9. 增加VRM MP7的穩定度。
10. add single broadcast

1.0.58
1. 優化MarcoService連線機制。
2. 修正連動解除會異常退出的問題。
3. 修正寫DO容易造成斷線的問題（加延遲）。

1.0.57
1. 優化Ai API效能。
2. 修正通用電表讀值。
3. 進一步避免非預期的DIO回傳值。
4. 水電表失聯警報後，如果有更新值，則直接解除警報。
5. 修正時間設定問題。
6. 延遲中繼回報回公司BA從60秒=>600秒。

1.0.56
1. Ai降速。
2. 加大Buffer。
3. 修正CO數值未顯示。
4. 新增巧力CIC BAW1A&2A電表。
5. 新增Soyal預設Port：1621,1632。
6. 通用Ai點優化。
7. 更新水電表類別清單。
8. 新增Gateway相關欄位。
9. 新增授權功能（未完成，暫時不做了）
10. 增通用太陽能。
11. 新增供外掛水表可以收即時值。

1.0.55
1. 新增組合型電表能用減的。
2. 修正Clone Nodes失效的問題
3. 修正點位太多時無法正常運作的問題。
4. DIO監控模組新增備註欄位，點位設定新增相對位址顯示。
5. 新增通用AI(暫只支援0x03、ModbusTCP、Float，BigEndian，Reversed）

1.0.54
1. 側邊選單警報計數功能，改為不計算時間設定之靜音節點。
2. 新增DIO 504 PORT。
3. 新增組合型電表顯示點位，一樣建立於CloneNodes，請於位址欄填入E$+電表ID，比如：「E$123,456」就會計算ID123跟ID456的電表KW總和，此功能如CloneNode，存檔後需人工確認後改資料庫。
4. *新增上方選單出現警報計數功能，僅有樓層圖面位址有此功能，首頁及其它設定等頁面不顯示。
*功能4需於Joomla後台「選單」->「Main Menu」中選取到相對應的連結，點擊進入後選擇Helix Mega Menu Options，填入Add Custom Class，內容為main-menu-id，id請置換成頁面當時網址列上的id如id=2657那麼要填入main-menu-2657，沒有填的選單不會有警報計數。
*功能4針對Joomla後台「元件」->「mymenu」中的樓層對應邏輯需確保全部數量正確，否則影響計算數量。

1.0.53
1. 新增點位可選是否顯示名稱功能。
2. 連動Timeout預設改為0。
3. 修正攝影機跳出邏輯判斷。
4. 新增通用CO裝置。
5. 新增首頁資料庫空間顯示。
6. 新增側邊樓層選單ALARM計數。


1.0.52
1. 修正通用OPC電表讀值。
2. 修正士林電機SPM8太陽能電表讀值。
3. 修正手動開啟攝影機會被關掉的問題。
4. 修正DI解除畫面不會自動更新的問題。

1.0.51
1. 修正屏榮空調溫度。
2. 改為半小時紀錄電表。
3. 新增兩段式（時間電價）計算功能。
4. 修改緊急求救攝影機問題。
5. 新增延遲關閉攝影機問題。
6. 修改攝影機Timeout預設為0（隨連動解除關閉，無延遲）。
★此版更新需要將所有連動遲時設為0，否則會被視為延遲關閉。

1.0.49
1. 修正DI解除時，攝影機會重開一次的問題。
2. 新增延遲解除DO的連動。
3. 連動延遲任務未完成前，當第二次觸發發生時，強迫取消之前未執行完的延遲DO。


1.0.48
1. 新增士林電機SPM8電表(接太陽能)。
2. 改善「紀錄」頁面效能。
3. 修正「警報設定」頁面存檔跳錯誤的問題。
4. 修正聲音停不了的問題。


1.0.47
1. 新增時間電費網頁版。
2. 新增依點位播放不同ALARM聲的功能，用資料庫修改，無介面
PS：要在後台的媒體新增alarm_audios資料夾，然後放basic_alarm.mp3做為預設ALARM聲，否則會無聲，媒體要新增合法的副檔名mp3、合法的 MIME 類型audio/mpeg
3. 修正對講機連動CCTV功能

1.0.46
1. 對講機上傳影片位置更換 ./video_uploads
2. 新增只有DI的二線控
3. 新增通用型 OPC DA水電表（Via OPC DA Gateway)

1.0.45
1. 修正中繼故障回報，延遲60秒
2. Kevin新增一支Upload api
3. 修正時間設定導致攝影機跳不出來問題

1.0.44
1. 新增屏榮空調AI(Through Omron PLC via EdgeLink device)，現有溫度及CO2點位。
2. 新增中繼故障回報機制 http://office.weema.com.tw:28080/relayAliveReportBack?name={siteid}

1.0.43
1. 修正Weema Outlet需量問題。
2. 修正需量無法重複觸發問題。
3. 新增時間設定及靜音節點功能。
4. 故障分析新增，沒選擇點位則顯示全部故障點。

1.0.42
1. 新增故障點設定及故障分析報表（下載XLSX）
2. 需新增PhpSpreadsheet-Joomla套件
3. 新增AEB DRB電表(AS 3P380)
4. 調整realtime api

1.0.41
1. 新增IRTI人形、人流連動
2. 新增Weema IAQ連動設定

1.0.40
1. 新增Weema IAQ(Over ModbusTCP)
2. 修正三相電表電壓問題
3. 新增IRTI人形、人流支援(Via IrtiIvaProxy)
4. 調整電表類顯示

1.0.39
1. 新增Weema Outlet支援(Via Ehome over ModbusTCP)

1.0.38
1. 調整巧力電表從只收KWH至全收
2. 新增 沙崙OPCDA電表
3. 修正台科電水表不更新問題
4. 修正桃園API值不更新問題

1.0.37
1. 修正時序的問題

1.0.36
1. 新增電力紀錄匯出Excel功能
2. 新增用水紀錄匯出Excel功能
3. 新增首頁mode:3 = 3支水表功能
4. 修正電力紀錄計算數值
5. 修正用水紀錄計算數值
6. 新增水電表失聯通知，每15分鐘檢查一次，每小時0~3分也會檢查一次
7. 新增可顯示前次觸發間隔時間功能


1.0.35
1. 修正VMR電表顯示問題
2. 修正無DI時可執行DO
3. 修正DO初始值
4. 修正電表KWH會變0
5. 修正時序由24dio處理
6. 修正alarm時間太長會不動作的問題，timeout會存在/opt/jobs裡，
   每秒會-1
7. 新增沙崙CO2支援
8. clear_alarm 多了一個is_do變數要不要做do_action
9. 在時序下alarm之前會先做clear_alarm，is_do = false
10. 修正大業電梯拆值公式

1.0.34
1. 修正timing的bug

1.0.33
1. 增大socket receive buffer
2. 新增VMR MP7電表
3. 新增大業(fuji)電梯，透過DI至24DIO
4. 變更永揚消防為ModbusTCP模式
5. 簡化電表更新內容
6. 新增電表CRC檢查
7. Catch SIGPIPE
8. 加回485的延遲
9. 對講機通話DI尾綴100功能修正 =>增加(call_fail)

1.0.32
1. 新增最後更新時間至圖表
2. 新增Clone Nodes功能（測試中）
3. 停用RS485中間的100ms (維持原狀，不停用)
4. 停用水電表http to ba LOG
5. 新增dio_type至節點title
6. 電表樓層版面調整
7. 修正jnc溫濕度的CO2連動功能
8. 新增溫濕度解除ALARM5%容忍值
9. 水表顯示值變更為小數點後2位
10. 修正SOYAL會停止問題
11. 優化即時更新的水電表資料（減少數量）


1.0.31
1. 停止舊的用電樹狀圖
2. 修正電力、用水樹狀圖數值錯誤問題
3. 修正血壓計問題
4. 新增ID顯示至點位
5. 新增對講機DI點尾綴100 => 通話開始（call_start），通話結束(call_ended)

1.0.30
1. 修正連動不會復歸的問題
2. 首頁水表計算新增台科電水表
3. 歷史紀錄文字變更

1.0.29
1. 將電力樹狀圖中的電表及太陽能分開
2. 更新水表，電表，太陽能點擊連結，連結至圖表頁面
3. 用水樹狀圖，電力樹狀圖版面調整置中

1.0.28
1. 修正首頁太陽能文字

1.0.27
1. 修正1.0.26更新的BUG

1.0.26
1. 新增台科電水表
2. 新增首頁太陽能計算
3. 新增電力樹狀圖v2 (測試中)
4. 延遲電表更新至1分鐘
5. marco_service 由https=>http
6. 不跳圖BUG修正

1.0.25
1. trigger_alarm = false : 改成有LOG
2. 縮短水表值更新時間至每1分鐘
3. 新增點位更新時間（滑鼠移過去顯示）
4. 新增新望太陽能支援
5. 調整水表更新策略

1.0.24
1. 修正Weather Dashboard顯示單位問題
2. 修正巧力電表取值公式
3. 新增首頁昨日總用水功能

1.0.23
1. 修正設定連動時的BUG
2. 修正首頁溫度顯示異常
3. 修正主電表不能存的問題
4. 修正首頁顯示文字：今日用電=>累積用電
5. 新增trigger_alarm，0則無紀錄無跳圖
6. 新增by user group 跳圖設定
7. 點位無名稱時隱藏灰框

1.0.22
廣播訊息有enable才送

1.0.21
1. 增加六川電梯DELAY
2. 增加六川電梯回應CHECKSUM檢查
3. 修正拉圖後列表會跑掉的問題
4. 修正24DIO的DO會變成DI的問題
add 電表值預設為string

1.0.20
1. 修正六川電梯取值
2. 修正久德土壤計取值
3. 修正士林電機電表取值
4. 新增JNC空氣品質
5. 修正大同電表取值
6. 調整用戶電表顯示方式

1.0.19
新增getPublicIP

1.0.18
1. 新增久德雨量計
2. 新增久德土壤計
3. 新增久德風向計

1.0.17
1. 修正NC NO存檔問題
2. 修正保創消防功能
3. 新增三菱電梯
4. 新增士林電機電表
5. 修正CO觸發問題

1.0.16
1. 新增保創消防支援
2. 新增首頁顯示版本號

1.0.15
1. SOYAL門禁改為DI + DO
2. 修正SOYAL會變成兩個DO的問題
3. 新增Bender PEM333, PEM555電表支援
4. 更新Marco_service 磐岳資料格式

1.0.14
1. 優化findMaxLogId
2. 刪除已停用的get_devicelogAlarm及相關function

1.0.13
1. 修正國際牌二線控在新增裝置時的BUG
2. fix findMaxLogId bug

1.0.12
fix floor update_open bug 
增加瓦斯抄寫
增加送住戶廣播訊息

1.0.11
1. 更新Marco_service（支援Weema weather dashboard app)
2. 補1.0.6 com_residents.zip

1.0.10
在取得backup.sql時，timeout時間為20秒

1.0.9
1. Weema社區新增姓名欄位
2. marco_service適配姓名欄位
3. marco_service 推撥訊息BUG修正

1.0.8
更新了 users 為1.1

1.0.7
1. 整合marco_service至com_site_settings更新

1.0.6
1. 新增WEEMA社區住戶、血壓功能
2. 24DIO=>marco_service api調整

1.0.5
1. 新增廣播訊息
在ba 警報設定下的通知廣播
設定的時候號碼用逗號分開
號碼對應的ip位址在app6的設定裡

2. 修正SOYAL門禁更新時站號未更新問題

1.0.4
1. 修正首頁MODE0的BUG

1.0.3
1. 新增永嘉溫濕度計
2. 修正SOYAL門禁只問一個裝置
3. SOYAL門禁改回原本有一個DO點的模式
4. 拿掉do_do DEBUG訊息
5. 首頁新增WIDGET模式，需對應SITE_SETTINGS: { “name": "HOME_WIDGET", "content": "{\"mode\":0}" }   (0-預設，1-中路一用)

